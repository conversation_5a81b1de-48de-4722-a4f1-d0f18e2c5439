"""Add unique constraint to source names and also add original file name column

Revision ID: 46699adc71a7
Revises: 1af251a42c06
Create Date: 2025-07-01 13:30:48.279151

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op
from letta.settings import settings

# revision identifiers, used by Alembic.
revision: str = "46699adc71a7"
down_revision: Union[str, None] = "1af251a42c06"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("files", sa.Column("original_file_name", sa.String(), nullable=True))

    # Handle existing duplicate source names before adding unique constraint
    connection = op.get_bind()

    # Find duplicates and rename them by appending a suffix
    result = connection.execute(
        sa.text(
            """
        WITH duplicates AS (
            SELECT name, organization_id,
                   ROW_NUMBER() OVER (PARTITION BY name, organization_id ORDER BY created_at) as rn,
                   id
            FROM sources
            WHERE (name, organization_id) IN (
                SELECT name, organization_id
                FROM sources
                GROUP BY name, organization_id
                HAVING COUNT(*) > 1
            )
        )
        SELECT id, name, rn
        FROM duplicates
        WHERE rn > 1
    """
        )
    )

    # Rename duplicates by appending a number suffix
    for row in result:
        source_id, original_name, duplicate_number = row
        new_name = f"{original_name}_{duplicate_number}"
        connection.execute(
            sa.text("UPDATE sources SET name = :new_name WHERE id = :source_id"), {"new_name": new_name, "source_id": source_id}
        )

    op.create_unique_constraint("uq_source_name_organization", "sources", ["name", "organization_id"])
    # ### end Alembic commands ###


def downgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("uq_source_name_organization", "sources", type_="unique")
    op.drop_column("files", "original_file_name")
    # ### end Alembic commands ###
