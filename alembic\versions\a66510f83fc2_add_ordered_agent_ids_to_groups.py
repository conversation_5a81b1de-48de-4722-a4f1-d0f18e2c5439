"""add ordered agent ids to groups

Revision ID: a66510f83fc2
Revises: bdddd421ec41
Create Date: 2025-03-27 11:11:51.709498

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op
from letta.settings import settings

# revision identifiers, used by Alembic.
revision: str = "a66510f83fc2"
down_revision: Union[str, None] = "bdddd421ec41"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("groups", sa.Column("agent_ids", sa.JSON(), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("groups", "agent_ids")
    # ### end Alembic commands ###
