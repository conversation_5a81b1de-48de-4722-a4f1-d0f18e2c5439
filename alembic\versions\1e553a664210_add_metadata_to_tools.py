"""Add metadata to Tools

Revision ID: 1e553a664210
Revises: 2cceb07c2384
Create Date: 2025-03-17 15:50:05.562302

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op
from letta.settings import settings

# revision identifiers, used by Alembic.
revision: str = "1e553a664210"
down_revision: Union[str, None] = "2cceb07c2384"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("tools", sa.Column("metadata_", sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("tools", "metadata_")
    # ### end Alembic commands ###
