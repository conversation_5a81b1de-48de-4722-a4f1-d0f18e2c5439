# syntax = docker/dockerfile:1.6

# Build a self-configuring postgres image with pgvector installed.
# It has no dependencies except for the base image.

# Build with:
# docker build -t letta-db -f db/Dockerfile.simple .
#
# -t letta-db: tag the image with the name letta-db (tag defaults to :latest)
# -f db/Dockerfile.simple: use the Dockerfile at db/Dockerfile.simple (this file)
# .: build the image from the current directory, not really used.

#
# Run the first time with:
# docker run -d --rm \
#   --name letta-db \
#   -p 5432:5432 \
#   -e POSTGRES_PASSWORD=password \
#   -v letta_db:/var/lib/postgresql/data \
#   letta-db:latest
#
# -d: run in the background
# --rm: remove the container when it exits
# --name letta-db: name the container letta-db
# -p 5432:5432: map port 5432 on the host to port 5432 in the container
# -v letta_db:/var/lib/postgresql/data: map the volume letta_db to /var/lib/postgresql/data in the container
# letta-db:latest: use the image letta-db:latest
#
# After the first time, you do not need the POSTGRES_PASSWORD.
# docker run -d --rm \
#   --name letta-db \
#   -p 5432:5432 \
#   -v letta_db:/var/lib/postgresql/data \
#   letta-db:latest

# Rather than a docker volume (letta_db), you can use an absolute path to a directory on the host.
#
# You can stop the container with:
#  docker stop letta-db
#
# You access the database with:
# postgresql+pg8000://user:password@localhost:5432/db
# where user, password, and db are the values you set in the init-letta.sql file,
# all defaulting to 'letta'.

# Version tags can be found here: https://hub.docker.com/r/ankane/pgvector/tags
ARG PGVECTOR=v0.5.1
# Set up a minimal postgres image
FROM ankane/pgvector:${PGVECTOR}
RUN sed -e 's/^    //' >/docker-entrypoint-initdb.d/01-initletta.sql <<'EOF'
    -- Title: Init Letta Database

    -- Fetch the docker secrets, if they are available.
    -- Otherwise fall back to environment variables, or hardwired 'letta'
    \set db_user `([ -r /var/run/secrets/letta-user ] && cat /var/run/secrets/letta-user) || echo "${LETTA_USER:-letta}"`
    \set db_password `([ -r /var/run/secrets/letta-password ] && cat /var/run/secrets/letta-password) || echo "${LETTA_PASSWORD:-letta}"`
    \set db_name `([ -r /var/run/secrets/letta-db ] && cat /var/run/secrets/letta-db) || echo "${LETTA_DB:-letta}"`

    CREATE USER :"db_user"
        WITH PASSWORD :'db_password'
        NOCREATEDB
        NOCREATEROLE
        ;

    CREATE DATABASE :"db_name"
        WITH
        OWNER = :"db_user"
        ENCODING = 'UTF8'
        LC_COLLATE = 'en_US.utf8'
        LC_CTYPE = 'en_US.utf8'
        LOCALE_PROVIDER = 'libc'
        TABLESPACE = pg_default
        CONNECTION LIMIT = -1;

    -- Set up our schema and extensions in our new database.
    \c :"db_name"

    CREATE SCHEMA :"db_name"
        AUTHORIZATION :"db_user";

    ALTER DATABASE :"db_name"
        SET search_path TO :"db_name";

    CREATE EXTENSION IF NOT EXISTS vector WITH SCHEMA :"db_name";

    DROP SCHEMA IF EXISTS public CASCADE;
EOF
