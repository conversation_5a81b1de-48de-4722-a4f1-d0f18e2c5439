{"cells": [{"cell_type": "markdown", "id": "6d3806ac-38f3-4999-bbed-953037bd0fd9", "metadata": {}, "source": ["# Letta Python Client \n", "Welcome to the Let<PERSON> tutorial! In this tutorial, we'll go through how to create a basic user-client for Let<PERSON> and create a custom agent with long term memory. \n", "\n", "Letta runs *agents-as-a-service*, so agents can run independently on a server. For this tutorial, we will be connecting to an existing Letta server via the Python client and the UI console. If you don't have a running server, see the [documentation](https://letta.readme.io/docs/running-a-letta-server) for instructions on how to create one.  "]}, {"cell_type": "markdown", "id": "7c0b6d6b-dbe6-412b-b129-6d7eb7d626a3", "metadata": {}, "source": ["## Part 0: In<PERSON><PERSON> Letta "]}, {"cell_type": "code", "execution_count": null, "id": "481d0976-d26b-46d2-ba74-8f2bb5556387", "metadata": {}, "outputs": [], "source": ["!pip install git+https://github.com/cpacker/MemGPT.git@tutorials"]}, {"cell_type": "markdown", "id": "a0484348-f7b2-48e3-9a2f-7d6495ef76e3", "metadata": {}, "source": ["## Part 1: Connecting to the Letta Client \n", "\n", "The Letta client connects to a running Letta service, specified by `base_url`. The client corresponds to a *single-user* (you), so requires an authentication token to let the service know who you are. \n", "\n"]}, {"cell_type": "code", "execution_count": 19, "id": "53ae2e1b-ad22-43c2-b3d8-92d591be8840", "metadata": {}, "outputs": [], "source": ["from letta import create_client\n", "\n", "base_url = \"http://**************:8083\"\n", "\n", "# TODO: replace with your token \n", "my_token = \"sk-...\" \n", "\n", "client = create_client(base_url=base_url, token=my_token) "]}, {"cell_type": "markdown", "id": "3c5c8651-e8aa-4423-b2b8-284bf6a01577", "metadata": {}, "source": ["### Viewing the developer portal \n", "Letta provides a portal interface for viewing and interacting with agents, data sources, tools, and more. You can enter `http://**************:8083` into your browser to load the developer portal, and enter in `my_token` to log in. \n", "\n", "<img src=\"./developer_portal_login.png\" width=\"800\">"]}, {"cell_type": "markdown", "id": "66e47b34-5feb-4660-85f0-14b5ee7f62b9", "metadata": {}, "source": ["## Part 2: Create an agent \n", "We'll first start with creating a basic Letta agent. "]}, {"cell_type": "code", "execution_count": null, "id": "24745606-b0fb-4157-a5cd-82fd0c26711f", "metadata": {}, "outputs": [], "source": ["basic_agent = client.create_agent(\n", "    name=\"basic_agent\", \n", ")\n", "print(f\"Created agent: {basic_agent.name}\")"]}, {"cell_type": "markdown", "id": "fcfb0d7b-b260-4bc0-8db2-c65f40e4afd5", "metadata": {}, "source": ["We can now send messages from the user to the agent by specifying the `agent_id`: "]}, {"cell_type": "code", "execution_count": null, "id": "a37bc9aa-4efb-4b4d-a6ce-f02505cb3240", "metadata": {}, "outputs": [], "source": ["from letta.client.utils import pprint \n", "\n", "response = client.user_message(agent_id=basic_agent.id, message=\"hello\") \n", "pprint(response.messages)"]}, {"cell_type": "markdown", "id": "9803140c-2b9d-426b-8812-9295806eb312", "metadata": {}, "source": ["### Chatting in the developer portal \n", "You can also chat with the agent inside of the developer portal. Try clicking the chat button in the agent view. \n", "\n", "<img src=\"./dev_portal_agent_chat.png\" width=\"800\">"]}, {"cell_type": "markdown", "id": "99ae20ec-e92e-4480-a652-b4aea28a6199", "metadata": {}, "source": ["### Adding Personalization\n", "We can now create a more customized agent, but specifying a custom `human` and `persona` field. \n", "* The *human* specifies the personalization information about the user interacting with the agent \n", "* The *persona* specifies the behavior and personality of the event\n", "\n", "What makes <PERSON><PERSON> unique is that the starting *persona* and *human* can change over time as the agent gains new information, enabling it to have evolving memory. We'll see an example of this later in the tutorial."]}, {"cell_type": "code", "execution_count": null, "id": "c0876410-4d70-490d-a798-39938b5ce941", "metadata": {}, "outputs": [], "source": ["# TODO: feel free to change the human and person to what you'd like \n", "persona = \\\n", "\"\"\"\n", "You are a friendly and helpful agent!\n", "\"\"\"\n", "\n", "human = \\\n", "\"\"\"\n", "I am an Accenture consultant with many specializations. My name is <PERSON>.\n", "\"\"\"\n", "\n", "custom_agent = client.create_agent(\n", "    name=\"custom_agent\", \n", "    human=human, \n", "    persona=persona\n", ")"]}, {"cell_type": "markdown", "id": "21293857-80e4-46e4-b628-3912fad038e9", "metadata": {}, "source": ["### Viewing memory \n", "You can view and edit the agent's memory inside of the developer console. There are two type of memory, *core* and *archival* memory: \n", "1. Core memory stores short-term memories in the LLM's context \n", "2. Archival memory stores long term memories in a vector database\n", "\n", "In this example, we'll look at how the agent can modify its core memory with new information. To see the agent's memory, click the \"Core Memory\" section on the developer console. \n", "\n", "<img src=\"./dev_portal_memory.png\" width=\"800\">"]}, {"cell_type": "markdown", "id": "d8fa13eb-ce4b-4e4f-81b6-9d6ef6fa67c2", "metadata": {}, "source": ["### Referencing memory \n", "Letta agents can customize their responses based on what memories they have stored. Try asking a question that related to the human and persona you provided. "]}, {"cell_type": "code", "execution_count": null, "id": "fddbefe5-3b94-4a08-aa50-d80fb581c747", "metadata": {}, "outputs": [], "source": ["response = client.user_message(agent_id=custom_agent.id, message=\"what do I work as?\") \n", "pprint(response.messages)"]}, {"cell_type": "markdown", "id": "30497119-e208-4a4e-b482-e7cfff346263", "metadata": {}, "source": ["### Evolving memory \n", "Letta agents have long term memory, and can evolve what they store in their memory over time. In the example below, we make a correction to the previously provided information. See how the agent processes this new information. "]}, {"cell_type": "code", "execution_count": null, "id": "679fa708-20ee-4e75-9222-b476f126bc6f", "metadata": {}, "outputs": [], "source": ["response = client.user_message(agent_id=custom_agent.id, message=\"Actually, my name is <PERSON>\") \n", "pprint(response.messages)"]}, {"cell_type": "markdown", "id": "686ac5a3-be63-4afd-97ae-b7d05219dd60", "metadata": {}, "source": ["Now, look back at the developer portal and at the agent's *core memory*. Do you see a change in the *human* section of the memory? "]}, {"cell_type": "markdown", "id": "878d2f49-a5a6-4483-9f69-7436bcf00cfb", "metadata": {}, "source": ["## Part 3: <PERSON><PERSON> \n", "Letta agents can be connected to custom tools. Currently, tools must be created by service administrators. However, you can add additional tools provided by the service administrator to the agent you create. "]}, {"cell_type": "markdown", "id": "35785d36-2674-4a00-937b-4c747e0fb6bf", "metadata": {}, "source": ["### View Available Tools "]}, {"cell_type": "code", "execution_count": null, "id": "c307a6f7-276b-49f5-8d3d-48aaaea221a7", "metadata": {}, "outputs": [], "source": ["tools = client.list_tools().tools\n", "for tool in tools: \n", "    print(f\"Tool: {tool.name} - {tool.json_schema['description']}\")"]}, {"cell_type": "markdown", "id": "318d19dc-b9dd-448c-ab5c-9c9311d21fad", "metadata": {}, "source": ["### Create a tool using agent in the developer portal \n", "Create an agent in the developer portal and toggle additional tools you want the agent to use. We recommend modifying the *persona* to notify the agent that it should be using the tools for certain tasks. \n", "\n", "\n", "<img src=\"./dev_portal_tools.png\" width=\"800\">"]}, {"cell_type": "markdown", "id": "aecdaa70-861a-43d5-b006-fecd90a8ed19", "metadata": {}, "source": ["## Part 4: Cleanup (optional) \n", "You can cleanup the agents you creating the following command to delete your agents: "]}, {"cell_type": "code", "execution_count": null, "id": "1320d9c9-170b-48a8-b5e8-70737b1a8aac", "metadata": {}, "outputs": [], "source": ["for agent in client.list_agents().agents: \n", "    client.delete_agent(agent[\"id\"])\n", "    print(f\"Deleted agent {agent['name']} with ID {agent['id']}\")"]}, {"cell_type": "markdown", "id": "510675a8-22bc-4f9f-9c79-91e2ffa9caf9", "metadata": {}, "source": ["## 🎉 Congrats, you're done with day 1 of <PERSON><PERSON>! \n", "For day 2, we'll go over how to connect *data sources* to <PERSON><PERSON> to run RAG agents. "]}], "metadata": {"kernelspec": {"display_name": "letta", "language": "python", "name": "letta"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}