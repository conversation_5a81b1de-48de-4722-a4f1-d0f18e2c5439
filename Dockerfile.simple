# 使用Python官方镜像
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# 复制项目文件
COPY pyproject.toml poetry.lock ./
COPY . .

# 安装Poetry
RUN pip install poetry==2.1.3

# 配置Poetry
RUN poetry config virtualenvs.create false

# 安装依赖
RUN poetry install --only=main --extras "server postgres"

# 安装额外的依赖
RUN pip install sqlite-vec

# 暴露端口
EXPOSE 8283

# 设置启动命令
CMD ["letta", "server", "--host", "0.0.0.0", "--port", "8283"]
