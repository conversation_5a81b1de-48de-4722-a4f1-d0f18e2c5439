"""add org agent id indices

Revision ID: 348214cbc081
Revises: dd049fbec729
Create Date: 2025-05-28 22:43:18.509397

"""

from typing import Sequence, Union

from alembic import op
from letta.settings import settings

# revision identifiers, used by Alembic.
revision: str = "348214cbc081"
down_revision: Union[str, None] = "dd049fbec729"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index("ix_agent_passages_org_agent", "agent_passages", ["organization_id", "agent_id"], unique=False)
    op.create_index("ix_messages_org_agent", "messages", ["organization_id", "agent_id"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_messages_org_agent", table_name="messages")
    op.drop_index("ix_agent_passages_org_agent", table_name="agent_passages")
    # ### end Alembic commands ###
