version: '3.8'

services:
  letta_db:
    # 使用阿里云镜像
    image: registry.cn-hangzhou.aliyuncs.com/ankane/pgvector:v0.5.1
    container_name: letta_postgres
    networks:
      default:
        aliases:
          - pgvector_db
          - letta-db
    environment:
      - POSTGRES_USER=${LETTA_PG_USER:-letta}
      - POSTGRES_PASSWORD=${LETTA_PG_PASSWORD:-letta}
      - POSTGRES_DB=${LETTA_PG_DB:-letta}
    volumes:
      - letta_pgdata:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "${LETTA_PG_PORT:-5432}:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U letta"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  letta_server:
    # 先尝试使用本地构建，如果失败再用官方镜像
    build:
      context: .
      dockerfile: Dockerfile.simple
    container_name: letta_server
    hostname: letta-server
    depends_on:
      letta_db:
        condition: service_healthy
    ports:
      - "8283:8283"
      - "4317:4317"  # OpenTelemetry
      - "4318:4318"  # OpenTelemetry HTTP
    env_file:
      - .env
    environment:
      - LETTA_PG_URI=postgresql://${LETTA_PG_USER:-letta}:${LETTA_PG_PASSWORD:-letta}@letta-db:5432/${LETTA_PG_DB:-letta}
      - LETTA_DEBUG=true
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_API_BASE=${OPENAI_API_BASE}
    restart: unless-stopped
    volumes:
      - letta_data:/root/.letta
    networks:
      - default

volumes:
  letta_pgdata:
    driver: local
  letta_data:
    driver: local

networks:
  default:
    driver: bridge
