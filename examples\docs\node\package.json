{"name": "@letta-ai/core", "version": "0.1.0", "private": true, "type": "module", "scripts": {"example": "node --no-warnings --import 'data:text/javascript,import { register } from \"node:module\"; import { pathToFileURL } from \"node:url\"; register(\"ts-node/esm\", pathToFileURL(\"./\"));' example.ts", "build": "tsc"}, "dependencies": {"@letta-ai/letta-client": "^0.1.131"}, "devDependencies": {"@types/node": "^22.12.0", "ts-node": "^10.9.2", "typescript": "^5.7.3"}}