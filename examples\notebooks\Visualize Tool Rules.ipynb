{"cells": [{"cell_type": "markdown", "id": "3511f97a-40ef-4ccf-893d-881db53a917e", "metadata": {}, "source": ["# Visualizing Tool Rules \n", "By default, Letta agents can theoretically choose whatever tool they want to call, and whehter or not continue execution. Although we can modify this behavior through prompting, it can sometimes be easier and more reliable to instead constrain the behavior of the agent. \n", "\n", "This tutorial will show you how to add *tool rules* to Letta agents and visualize the execution graph. \n", "\n", "Make sure you have the following packages installed: \n", "* `letta-client`\n", "* `networkx`\n", "* `mat<PERSON><PERSON><PERSON>b`"]}, {"cell_type": "code", "execution_count": 28, "id": "332a5f53-c2c7-4b8f-950a-906fb1386962", "metadata": {}, "outputs": [], "source": ["from letta_client import Letta, TerminalToolRule, ConditionalToolRule, InitToolRule, ChildToolRule"]}, {"cell_type": "markdown", "id": "31cc2bf2-af49-4a09-9754-1b5ac8c1b6f4", "metadata": {}, "source": ["## Start the server\n", "\n", "Make sure you have a Letta server running that you can connect to. You can have a server running by: \n", "* Starting the [Letta Desktop](https://docs.letta.com/install) app on your computer \n", "* Running the [Docker container](https://docs.letta.com/quickstart/docker) "]}, {"cell_type": "code", "execution_count": 3, "id": "0fbdd4a5-442b-4095-88f7-bfb9506e362d", "metadata": {}, "outputs": [], "source": ["client = Letta(base_url=\"http://localhost:8283\")"]}, {"cell_type": "markdown", "id": "5c65418c-41e6-42bf-b7a6-3d1471f9e0e5", "metadata": {}, "source": ["## Defining tool rules \n", "We will use the default Letta tools, but all the following constraints: \n", "* `archival_memory_search` must be called first when the agent is invoked\n", "* `conversation_search` must be called if `archival_memory_search` is called\n", "* If `send_message` is called (what allows the agent to send a message to the user), then the agent will stop execution"]}, {"cell_type": "code", "execution_count": 5, "id": "45a66c16-60f9-4a1e-a36d-ed52714134dc", "metadata": {}, "outputs": [], "source": ["agent_state = client.agents.create(\n", "    memory_blocks = [\n", "        {\"label\": \"persona\", \"value\": \"I am a helpful agent\"}, \n", "        {\"label\": \"human\", \"value\": \"Name: <PERSON>\"}\n", "    ], \n", "    tool_rules = [\n", "        InitToolRule(tool_name=\"archival_memory_search\", type=\"run_first\"), \n", "        ChildToolRule(tool_name=\"archival_memory_search\", children=[\"conversation_search\"], type=\"constrain_child_tools\"), \n", "        TerminalToolRule(tool_name=\"send_message\", type=\"exit_loop\")              \n", "    ], \n", "    model=\"openai/gpt-4o-mini\", # specify the handle of the model you want to use\n", "    embedding=\"openai/text-embedding-3-small\" # specify the handle of the embedding model \n", ")"]}, {"cell_type": "code", "execution_count": 6, "id": "59ad7756-7a99-4844-81ec-ce26a30d7b85", "metadata": {}, "outputs": [{"data": {"text/plain": ["[InitToolRule(tool_name='archival_memory_search', type='run_first'),\n", " ChildToolRule(tool_name='archival_memory_search', type='constrain_child_tools', children=['conversation_search']),\n", " InitToolRule(tool_name='send_message', type='exit_loop')]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["agent_state.tool_rules"]}, {"cell_type": "code", "execution_count": 16, "id": "26fc7ce0-f8ca-4f30-ab5a-cd031488b3f4", "metadata": {}, "outputs": [], "source": ["response = client.agents.messages.create(\n", "    agent_id=agent_state.id,\n", "    messages=[\n", "        {\"role\": \"user\", \"content\": \"hello\"} \n", "    ],\n", ")"]}, {"cell_type": "markdown", "id": "d0d9222b-5f3a-4211-a190-d317843ecbe4", "metadata": {}, "source": ["We can see that the agent calls tools in the pattern that we expect: "]}, {"cell_type": "code", "execution_count": 19, "id": "9598c1dc-8923-4576-a9f8-2389d38c2176", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ToolCallMessage(id='message-e0171629-0fd8-476b-a473-4584b92b4772', date=datetime.datetime(2025, 2, 13, 3, 5, 56, tzinfo=TzInfo(UTC)), message_type='tool_call_message', tool_call=ToolCall(name='archival_memory_search', arguments='{\\n  \"query\": \"hello\",\\n  \"page\": 0,\\n  \"start\": 0,\\n  \"request_heartbeat\": true\\n}', tool_call_id='call_16fMrU8J6JJgJbiqrVVt7KTa'))\n", "ToolReturnMessage(id='message-94624be0-ed62-471d-8c3e-60fea7d56d7f', date=datetime.datetime(2025, 2, 13, 3, 5, 58, tzinfo=TzInfo(UTC)), message_type='tool_return_message', tool_return='([], 0)', status='success', tool_call_id='call_16fMrU8J6JJgJbiqrVVt7KTa', stdout=None, stderr=None)\n", "ToolCallMessage(id='message-003b0c97-d153-456b-8fec-478d03c6176a', date=datetime.datetime(2025, 2, 13, 3, 5, 59, tzinfo=TzInfo(UTC)), message_type='tool_call_message', tool_call=ToolCall(name='conversation_search', arguments='{\\n  \"query\": \"hello\",\\n  \"page\": 0,\\n  \"request_heartbeat\": true\\n}', tool_call_id='call_SaCTgxuLovFyyIqyxhMzfLaJ'))\n", "ToolReturnMessage(id='message-82ec1477-1f82-4058-b957-da2edecf5641', date=datetime.datetime(2025, 2, 13, 3, 5, 59, tzinfo=TzInfo(UTC)), message_type='tool_return_message', tool_return='Showing 1 of 1 results (page 0/0): [\\n  \"{\\\\n  \\\\\"type\\\\\": \\\\\"user_message\\\\\",\\\\n  \\\\\"message\\\\\": \\\\\"hello\\\\\",\\\\n  \\\\\"time\\\\\": \\\\\"2025-02-12 07:05:54 PM PST-0800\\\\\"\\\\n}\"\\n]', status='success', tool_call_id='call_SaCTgxuLovFyyIqyxhMzfLaJ', stdout=None, stderr=None)\n", "AssistantMessage(id='message-454127c9-7ee1-46da-8d43-a0b8cf6845c5', date=datetime.datetime(2025, 2, 13, 3, 6, tzinfo=TzInfo(UTC)), message_type='assistant_message', content=\"Hey there! It's great to see you here. How's your day going?\")\n"]}], "source": ["from pprint import pprint\n", "for message in response.messages: \n", "    if message.message_type == \"reasoning_message\": continue \n", "    pprint(message)"]}, {"cell_type": "markdown", "id": "5d1e0d9f-8ec7-43aa-a9b2-a8c46364751d", "metadata": {}, "source": ["## Visualizing Tool Rules \n", "We can visualize what tools the agent can call by using the `networkx` library to plot the relationship between tools. "]}, {"cell_type": "code", "execution_count": 26, "id": "a2ef505b-9b55-4f45-b4e0-247b9419c132", "metadata": {}, "outputs": [], "source": ["import networkx as nx\n", "import matplotlib.pyplot as plt\n", "\n", "def create_tool_sequence_graph(agent_state):\n", "    \"\"\"\n", "    Create a directed graph showing possible tool execution sequences based on given rules.\n", "    \n", "    Args:\n", "        agent_state: Agent state object containing tools and rules\n", "    \"\"\"\n", "    # Create directed graph\n", "    G = nx.DiGraph()\n", "    \n", "    # Add start and end nodes\n", "    G.add_node(\"START\")\n", "    G.add_node(\"END\")\n", "    \n", "    # Add all tools as nodes\n", "    for tool in agent_state.tools:\n", "        G.add_node(tool.name)\n", "    \n", "    # Process rules\n", "    start_tool = None\n", "    exit_tools = set()\n", "    constraints = {}\n", "    \n", "    # First pass: categorize rules\n", "    for rule in agent_state.tool_rules:\n", "        if rule.type == \"run_first\":\n", "            start_tool = rule.tool_name\n", "        elif rule.type == \"exit_loop\":\n", "            exit_tools.add(rule.tool_name)\n", "        elif rule.type == \"constrain_child_tools\":\n", "            constraints[rule.tool_name] = rule.children\n", "            \n", "    # If no start tool specified, connect START to all tools\n", "    if start_tool is None:\n", "        for tool in agent_state.tools:\n", "            G.add_edge(\"START\", tool.name)\n", "    else:\n", "        G.add_edge(\"START\", start_tool)\n", "    \n", "    # Add edges between tools based on rules\n", "    for source in agent_state.tools:\n", "        source_name = source.name\n", "        if source_name in exit_tools:\n", "            # Connect exit tools to END node\n", "            G.add_edge(source_name, \"END\")\n", "            continue\n", "            \n", "        if source_name in constraints:\n", "            # Only add edges to constrained children\n", "            for child in constraints[source_name]:\n", "                G.add_edge(source_name, child)\n", "        else:\n", "            # Add edges to all tools except those that must come first\n", "            G.add_edge(source_name,  \"END\")\n", "            for target in agent_state.tools:\n", "                target_name = target.name\n", "                if start_tool and target_name == start_tool:\n", "                    continue\n", "                G.add_edge(source_name, target_name)\n", "    \n", "    \n", "    # Create hierarchical layout\n", "    pos = nx.kamada_kawai_layout(G)\n", "    #pos = nx.nx_agraph.graphviz_layout(G, prog=\"dot\")\n", "    # Place START on the far left\n", "    #pos[\"START\"] = (-1, 0)\n", "    \n", "    # Place END on the far right\n", "    #pos[\"END\"] = (1, 0)\n", "    \n", "    # Create figure\n", "    plt.figure(figsize=(15, 10))\n", "    \n", "    # Draw nodes with different colors and sizes\n", "    node_colors = {\n", "        'START': 'lightgreen',\n", "        'END': 'lightcoral',\n", "        'default': 'lightblue'\n", "    }\n", "    \n", "    # Draw regular nodes\n", "    tool_nodes = list(set(G.nodes()) - {'START', 'END'})\n", "    nx.draw_networkx_nodes(G, pos, nodelist=tool_nodes, \n", "                          node_color=node_colors['default'], \n", "                          node_size=3000, \n", "                          node_shape='o')\n", "    \n", "    # Draw START node\n", "    nx.draw_networkx_nodes(G, pos, nodelist=['START'], \n", "                          node_color=node_colors['START'], \n", "                          node_size=3000, \n", "                          node_shape='o')\n", "    \n", "    # Draw END node\n", "    nx.draw_networkx_nodes(G, pos, nodelist=['END'], \n", "                          node_color=node_colors['END'], \n", "                          node_size=3000, \n", "                          node_shape='o')\n", "    \n", "    # Draw edges with arrows\n", "    nx.draw_networkx_edges(G, pos, \n", "                          edge_color='gray', \n", "                          arrows=True, \n", "                          arrowsize=10, \n", "                          #arrowstyle='->', \n", "                          width=2, node_size=3000)\n", "    \n", "    # Add labels with custom font\n", "    nx.draw_networkx_labels(G, pos, \n", "                           font_size=10, \n", "                           font_weight='bold', \n", "                           font_family='sans-serif')\n", "    \n", "    \n", "    plt.axis('off')\n", "    return G, plt"]}, {"cell_type": "code", "execution_count": 27, "id": "972ca7f8-bc4a-4183-b586-9f0212ade50b", "metadata": {}, "outputs": [{"data": {"text/plain": ["(<networkx.classes.digraph.DiGraph at 0x7fdc210eba30>,\n", " <module 'matplotlib.pyplot' from '/usr/local/anaconda3/lib/python3.9/site-packages/matplotlib/pyplot.py'>)"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1080x720 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["create_tool_sequence_graph(agent_state)"]}, {"cell_type": "code", "execution_count": null, "id": "0d545261-20db-43de-a057-1243e9b099ff", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "letta-cloud", "language": "python", "name": "letta-cloud"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}}, "nbformat": 4, "nbformat_minor": 5}