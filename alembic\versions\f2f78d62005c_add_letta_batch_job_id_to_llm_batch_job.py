"""Add letta batch job id to llm_batch_job

Revision ID: f2f78d62005c
Revises: c3b1da3d1157
Create Date: 2025-04-17 15:58:43.705483

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op
from letta.settings import settings

# revision identifiers, used by Alembic.
revision: str = "f2f78d62005c"
down_revision: Union[str, None] = "c3b1da3d1157"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("llm_batch_job", sa.Column("letta_batch_job_id", sa.String(), nullable=False))
    op.create_foreign_key(None, "llm_batch_job", "jobs", ["letta_batch_job_id"], ["id"], ondelete="CASCADE")
    # ### end Alembic commands ###


def downgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "llm_batch_job", type_="foreignkey")
    op.drop_column("llm_batch_job", "letta_batch_job_id")
    # ### end Alembic commands ###
