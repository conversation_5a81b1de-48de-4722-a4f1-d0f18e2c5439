"""support for project_id for blocks and groups

Revision ID: 06fbbf65d4f1
Revises: f55542f37641
Create Date: 2025-07-21 15:07:32.133538

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "06fbbf65d4f1"
down_revision: Union[str, None] = "f55542f37641"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("block", sa.Column("project_id", sa.String(), nullable=True))
    op.add_column("groups", sa.Column("project_id", sa.String(), nullable=True))

    # NOTE: running the backfill on alembic will result in locking with running application.
    # This is okay if okay with downtime. Options also to do rolling migration or dynamic updates.

    # Backfill project_id for blocks table
    # Since all agents for a block have the same project_id, we can just grab the first one
    # op.execute(
    #     text(
    #         """
    #     UPDATE block
    #     SET project_id = (
    #         SELECT a.project_id
    #         FROM blocks_agents ba
    #         JOIN agents a ON ba.agent_id = a.id
    #         WHERE ba.block_id = block.id
    #         AND a.project_id IS NOT NULL
    #         LIMIT 1
    #     )
    # """
    #     )
    # )

    # Backfill project_id for groups table
    # op.execute(
    #     text(
    #         """
    #     UPDATE groups
    #     SET project_id = (
    #         SELECT a.project_id
    #         FROM groups_agents ga
    #         JOIN agents a ON ga.agent_id = a.id
    #         WHERE ga.group_id = groups.id
    #         AND a.project_id IS NOT NULL
    #         LIMIT 1
    #     )
    # """
    #     )
    # )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("groups", "project_id")
    op.drop_column("block", "project_id")
    # ### end Alembic commands ###
