"""Add start end for agent file

Revision ID: 4537f0996495
Revises: 06fbbf65d4f1
Create Date: 2025-07-25 17:44:26.748765

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "4537f0996495"
down_revision: Union[str, None] = "06fbbf65d4f1"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("files_agents", sa.Column("start_line", sa.Integer(), nullable=True))
    op.add_column("files_agents", sa.Column("end_line", sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("files_agents", "end_line")
    op.drop_column("files_agents", "start_line")
    # ### end Alembic commands ###
