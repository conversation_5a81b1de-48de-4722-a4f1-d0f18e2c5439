events {
}
http {
    server {
        listen 80;
        listen [::]:80;
        listen 8283;
        listen [::]:8283;
        server_name letta.localhost;
        set $api_target "http://letta-server:8283";
        location / {
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $remote_addr;
            proxy_set_header X-Forwarded-Proto $scheme;
            resolver 127.0.0.11; # docker dns
            proxy_pass $api_target;
        }
    }
    map $http_upgrade $connection_upgrade {
    default upgrade;
    ''      close;
    }
    server {
        listen 80 default_server;
        server_name not_found;
        return 404;
    }
}
