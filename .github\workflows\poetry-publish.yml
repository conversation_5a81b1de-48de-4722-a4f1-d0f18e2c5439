name: poetry-publish
on:
  release:
    types: [published]
  workflow_dispatch:

jobs:
  build-and-publish:
    name: Build and Publish to PyPI
    if: github.repository == 'letta-ai/letta'  # TODO: if the repo org ever changes, this must be updated
    runs-on: ubuntu-latest
    steps:
      - name: Check out the repository
        uses: actions/checkout@v4

      - name: "Setup Python, Poetry and Dependencies"
        uses: packetcoders/action-setup-cache-python-poetry@main
        with:
          python-version: "3.11"
          poetry-version: "1.7.1"

      - name: Configure poetry
        env:
          PYPI_TOKEN: ${{ secrets.PYPI_TOKEN }}
        run: |
          poetry config pypi-token.pypi "$PYPI_TOKEN"

      - name: Build the Python package
        run: poetry build

      - name: Publish the package to PyPI
        run: poetry publish
