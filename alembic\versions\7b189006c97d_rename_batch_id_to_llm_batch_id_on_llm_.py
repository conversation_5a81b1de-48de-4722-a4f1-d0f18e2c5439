"""Rename batch_id to llm_batch_id on llm_batch_item

Revision ID: 7b189006c97d
Revises: f2f78d62005c
Create Date: 2025-04-17 16:04:52.045672

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op
from letta.settings import settings

# revision identifiers, used by Alembic.
revision: str = "7b189006c97d"
down_revision: Union[str, None] = "f2f78d62005c"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("llm_batch_items", sa.Column("llm_batch_id", sa.String(), nullable=False))
    op.drop_index("ix_llm_batch_items_batch_id", table_name="llm_batch_items")
    op.create_index("ix_llm_batch_items_llm_batch_id", "llm_batch_items", ["llm_batch_id"], unique=False)
    op.drop_constraint("llm_batch_items_batch_id_fkey", "llm_batch_items", type_="foreignkey")
    op.create_foreign_key(None, "llm_batch_items", "llm_batch_job", ["llm_batch_id"], ["id"], ondelete="CASCADE")
    op.drop_column("llm_batch_items", "batch_id")
    # ### end Alembic commands ###


def downgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("llm_batch_items", sa.Column("batch_id", sa.VARCHAR(), autoincrement=False, nullable=False))
    op.drop_constraint(None, "llm_batch_items", type_="foreignkey")
    op.create_foreign_key("llm_batch_items_batch_id_fkey", "llm_batch_items", "llm_batch_job", ["batch_id"], ["id"], ondelete="CASCADE")
    op.drop_index("ix_llm_batch_items_llm_batch_id", table_name="llm_batch_items")
    op.create_index("ix_llm_batch_items_batch_id", "llm_batch_items", ["batch_id"], unique=False)
    op.drop_column("llm_batch_items", "llm_batch_id")
    # ### end Alembic commands ###
