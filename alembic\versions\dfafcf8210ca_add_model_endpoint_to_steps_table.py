"""add model endpoint to steps table

Revision ID: dfafcf8210ca
Revises: f922ca16e42c
Create Date: 2025-02-04 16:45:34.132083

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op
from letta.settings import settings

# revision identifiers, used by Alembic.
revision: str = "dfafcf8210ca"
down_revision: Union[str, None] = "f922ca16e42c"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("steps", sa.Column("model_endpoint", sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("steps", "model_endpoint")
    # ### end Alembic commands ###
