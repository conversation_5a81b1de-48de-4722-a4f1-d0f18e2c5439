#!/usr/bin/env python3
"""
直接API测试
"""

import requests
import json
import os

# 设置环境变量
os.environ["OPENAI_API_KEY"] = "ms-3d98807d-54de-4c4b-93c2-38d79c93f004"
os.environ["OPENAI_API_BASE"] = "https://api-inference.modelscope.cn/v1"

def test_create_agent():
    """测试创建智能体"""
    url = "http://localhost:8283/v1/agents/"
    
    payload = {
        "name": "测试助手",
        "memory_blocks": [
            {
                "label": "human",
                "value": "用户名：测试用户"
            },
            {
                "label": "persona", 
                "value": "你是一个友好的AI助手"
            }
        ],
        "model": "gpt-4o-mini",
        "embedding": "text-embedding-3-small"
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print("🔄 正在创建智能体...")
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            agent_data = response.json()
            print(f"✅ 成功创建智能体: {agent_data.get('name')} (ID: {agent_data.get('id')})")
            return agent_data.get('id')
        else:
            print(f"❌ 创建失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def test_list_agents():
    """测试列出智能体"""
    url = "http://localhost:8283/v1/agents/"
    
    try:
        print("🔄 获取智能体列表...")
        response = requests.get(url, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            agents = response.json()
            print(f"✅ 当前智能体数量: {len(agents)}")
            for agent in agents:
                print(f"   - {agent.get('name')} (ID: {agent.get('id')})")
        else:
            print(f"❌ 获取失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def main():
    print("🧪 开始直接API测试...")
    
    # 先列出现有智能体
    test_list_agents()
    
    print("\n" + "="*50)
    
    # 尝试创建智能体
    agent_id = test_create_agent()
    
    if agent_id:
        print(f"\n🎉 测试成功！智能体ID: {agent_id}")
    else:
        print("\n❌ 测试失败")

if __name__ == "__main__":
    main()
