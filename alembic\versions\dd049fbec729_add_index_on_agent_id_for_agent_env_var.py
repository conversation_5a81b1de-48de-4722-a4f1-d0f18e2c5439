"""Add index on agent_id for agent env var

Revision ID: dd049fbec729
Revises: 9ecbdbaa409f
Create Date: 2025-05-23 17:41:48.235405

"""

from typing import Sequence, Union

from alembic import op
from letta.settings import settings

# revision identifiers, used by Alembic.
revision: str = "dd049fbec729"
down_revision: Union[str, None] = "9ecbdbaa409f"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index("idx_agent_environment_variables_agent_id", "agent_environment_variables", ["agent_id"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("idx_agent_environment_variables_agent_id", table_name="agent_environment_variables")
    # ### end Alembic commands ###
