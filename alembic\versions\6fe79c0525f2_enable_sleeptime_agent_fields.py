"""enable sleeptime agent fields

Revision ID: 6fe79c0525f2
Revises: e991d2e3b428
Create Date: 2025-04-02 08:32:57.412903

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op
from letta.settings import settings

# revision identifiers, used by Alembic.
revision: str = "6fe79c0525f2"
down_revision: Union[str, None] = "e991d2e3b428"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("agents", sa.Column("enable_sleeptime", sa.<PERSON>(), nullable=True))
    op.alter_column("groups", "background_agents_interval", new_column_name="background_agents_frequency")
    # ### end Alembic commands ###


def downgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("groups", "background_agents_frequency", new_column_name="background_agents_interval")
    op.drop_column("agents", "enable_sleeptime")
    # ### end Alembic commands ###
