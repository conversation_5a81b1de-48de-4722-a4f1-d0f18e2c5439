"""Add message_buffer_autoclear option for AgentState

Revision ID: 7980d239ea08
Revises: dfafcf8210ca
Create Date: 2025-02-12 14:02:00.918226

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op
from letta.settings import settings

# revision identifiers, used by Alembic.
revision: str = "7980d239ea08"
down_revision: Union[str, None] = "dfafcf8210ca"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # Add the column with a temporary nullable=True so we can backfill
    op.add_column("agents", sa.Column("message_buffer_autoclear", sa.<PERSON>(), nullable=True))

    # Backfill existing rows to set message_buffer_autoclear to False where it's NULL
    op.execute("UPDATE agents SET message_buffer_autoclear = false WHERE message_buffer_autoclear IS NULL")

    # Now, enforce nullable=False after backfilling
    op.alter_column("agents", "message_buffer_autoclear", nullable=False)


def downgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("agents", "message_buffer_autoclear")
    # ### end Alembic commands ###
