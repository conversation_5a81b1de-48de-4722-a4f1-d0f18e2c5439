"""Add block history tables

Revision ID: bff040379479
Revises: a66510f83fc2
Create Date: 2025-03-31 14:49:30.449052

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op
from letta.settings import settings

# revision identifiers, used by Alembic.
revision: str = "bff040379479"
down_revision: Union[str, None] = "a66510f83fc2"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "block_history",
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("label", sa.String(), nullable=False),
        sa.Column("value", sa.Text(), nullable=False),
        sa.Column("limit", sa.<PERSON>nteger(), nullable=False),
        sa.Column("metadata_", sa.JSON(), nullable=True),
        sa.Column("actor_type", sa.String(), nullable=True),
        sa.Column("actor_id", sa.String(), nullable=True),
        sa.Column("block_id", sa.String(), nullable=False),
        sa.Column("sequence_number", sa.Integer(), nullable=False),
        sa.Column("organization_id", sa.String(), nullable=False),
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("is_deleted", sa.Boolean(), server_default=sa.text("FALSE"), nullable=False),
        sa.Column("_created_by_id", sa.String(), nullable=True),
        sa.Column("_last_updated_by_id", sa.String(), nullable=True),
        sa.ForeignKeyConstraint(["block_id"], ["block.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(
            ["organization_id"],
            ["organizations.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index("ix_block_history_block_id_sequence", "block_history", ["block_id", "sequence_number"], unique=True)
    op.add_column("block", sa.Column("current_history_entry_id", sa.String(), nullable=True))
    op.add_column("block", sa.Column("version", sa.Integer(), server_default="1", nullable=False))
    op.create_index(op.f("ix_block_current_history_entry_id"), "block", ["current_history_entry_id"], unique=False)
    op.create_foreign_key("fk_block_current_history_entry", "block", "block_history", ["current_history_entry_id"], ["id"], use_alter=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("fk_block_current_history_entry", "block", type_="foreignkey")
    op.drop_index(op.f("ix_block_current_history_entry_id"), table_name="block")
    op.drop_column("block", "version")
    op.drop_column("block", "current_history_entry_id")
    op.drop_index("ix_block_history_block_id_sequence", table_name="block_history")
    op.drop_table("block_history")
    # ### end Alembic commands ###
