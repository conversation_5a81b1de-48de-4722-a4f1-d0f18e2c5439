"""add stop reasons to steps and message error flag

Revision ID: cce9a6174366
Revises: 2c059cad97cc
Create Date: 2025-07-10 13:56:17.383612

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "cce9a6174366"
down_revision: Union[str, None] = "2c059cad97cc"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("messages", sa.Column("is_err", sa.<PERSON>(), nullable=True))

    # manually added to handle non-table creation enums
    stopreasontype = sa.Enum(
        "end_turn", "error", "invalid_tool_call", "max_steps", "no_tool_call", "tool_rule", "cancelled", name="stopreasontype"
    )
    stopreasontype.create(op.get_bind())
    op.add_column("steps", sa.Column("stop_reason", stopreasontype, nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("steps", "stop_reason")
    op.drop_column("messages", "is_err")

    stopreasontype = sa.Enum(name="stopreasontype")
    stopreasontype.drop(op.get_bind())
    # ### end Alembic commands ###
