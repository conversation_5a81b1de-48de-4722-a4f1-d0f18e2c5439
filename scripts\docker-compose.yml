version: '3.7'
services:
  redis:
    image: redis:alpine
    container_name: redis
    healthcheck:
      test: ['CMD-SHELL', 'redis-cli ping | grep PONG']
      interval: 1s
      timeout: 3s
      retries: 5
    ports:
      - '6379:6379'
    volumes:
      - ./data/redis:/data
    command: redis-server --appendonly yes
  postgres:
    image: ankane/pgvector
    container_name: postgres
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U postgres']
      interval: 1s
      timeout: 3s
      retries: 5
    ports:
      - '5432:5432'
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: letta
    volumes:
      - ./data/postgres:/var/lib/postgresql/data
      - ./scripts/postgres-db-init/init.sql:/docker-entrypoint-initdb.d/init.sql
