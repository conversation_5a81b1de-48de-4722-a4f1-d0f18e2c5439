"""add bedrock creds to byok

Revision ID: 9556081ce65b
Revises: 90fd814d0cda
Create Date: 2025-06-18 11:15:39.461916

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op
from letta.settings import settings

# revision identifiers, used by Alembic.
revision: str = "9556081ce65b"
down_revision: Union[str, None] = "90fd814d0cda"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("providers", sa.Column("access_key", sa.String(), nullable=True))
    op.add_column("providers", sa.Column("region", sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("providers", "region")
    op.drop_column("providers", "access_key")
    # ### end Alembic commands ###
