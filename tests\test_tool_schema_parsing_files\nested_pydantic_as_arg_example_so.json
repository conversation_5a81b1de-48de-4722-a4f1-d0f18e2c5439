{"name": "create_task_plan", "description": "Creates a task plan for the current task.\n\nIt takes in a list of steps, and updates the task with the new steps provided.\nIf there are any current steps, they will be overwritten.\nEach step in the list should have the following format:\n{\n    \"name\": <string> -- Name of the step.\n    \"key\": <string> -- Unique identifier for the step.\n    \"description\": <string> -- An exhaustic description of what this step is trying to achieve and accomplish.\n}", "strict": true, "parameters": {"type": "object", "properties": {"steps": {"type": "object", "description": "List of steps to add to the task plan.", "properties": {"steps": {"type": "array", "description": "A list of steps to add to the task plan.", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the step."}, "key": {"type": "string", "description": "Unique identifier for the step."}, "description": {"type": "string", "description": "An exhaustic description of what this step is trying to achieve and accomplish."}}, "additionalProperties": false, "required": ["name", "key", "description"]}}}, "additionalProperties": false, "required": ["steps"]}, "completed": {"type": "integer", "description": "The number of steps to add as completed to the task plan."}}, "additionalProperties": false, "required": ["steps", "completed"]}}