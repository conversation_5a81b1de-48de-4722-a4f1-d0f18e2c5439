"""rename sleeptime_agent_frequency

Revision ID: 90bb156e71df
Revises: 6fe79c0525f2
Create Date: 2025-04-03 17:20:26.218596

"""

from typing import Sequence, Union

from alembic import op
from letta.settings import settings

# revision identifiers, used by Alembic.
revision: str = "90bb156e71df"
down_revision: Union[str, None] = "6fe79c0525f2"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("groups", "background_agents_frequency", new_column_name="sleeptime_agent_frequency")
    # ### end Alembic commands ###


def downgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("groups", "sleeptime_agent_frequency", new_column_name="background_agents_frequency")
    # ### end Alembic commands ###
