{"cells": [{"cell_type": "code", "execution_count": null, "id": "fb13c7bc-fbb4-4ccd-897c-08995db258e8", "metadata": {}, "outputs": [], "source": ["from letta import Admin \n", "\n", "base_url=\"letta.localhost\"\n", "token=\"lettaadmin\" \n", "\n", "admin_client = Admin(base_url=base_url, token=\"lettaadmin\")"]}, {"cell_type": "code", "execution_count": null, "id": "984b8249-a3f7-40d1-9691-4d128f9a90ff", "metadata": {}, "outputs": [], "source": ["user = admin_client.create_user()"]}], "metadata": {"kernelspec": {"display_name": "letta", "language": "python", "name": "letta"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}