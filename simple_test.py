#!/usr/bin/env python3
"""
简单的Letta连接测试
"""

import requests
import json

def test_server_health():
    """测试服务器健康状态"""
    try:
        # 尝试访问根端点
        response = requests.get("http://localhost:8283/", timeout=10)
        print(f"根端点状态码: {response.status_code}")
        if response.status_code in [200, 404]:  # 404也表示服务器在运行
            print("✅ 服务器正在运行")
            return True
        else:
            print(f"❌ 服务器响应异常: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        return False

def test_api_endpoints():
    """测试API端点"""
    try:
        # 测试根端点
        response = requests.get("http://localhost:8283/", timeout=10)
        print(f"根端点状态码: {response.status_code}")
        
        # 测试v1端点
        response = requests.get("http://localhost:8283/v1", timeout=10)
        print(f"v1端点状态码: {response.status_code}")
        
        # 测试agents端点
        response = requests.get("http://localhost:8283/v1/agents", timeout=10)
        print(f"agents端点状态码: {response.status_code}")
        if response.status_code == 200:
            agents = response.json()
            print(f"当前智能体数量: {len(agents)}")
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")

def main():
    print("🔍 开始简单连接测试...")
    
    # 测试服务器健康状态
    if test_server_health():
        print("\n🔗 测试API端点...")
        test_api_endpoints()
    
    print("\n✅ 测试完成")

if __name__ == "__main__":
    main()
