"""add project and template id to agent

Revision ID: f922ca16e42c
Revises: 6fbe9cace832
Create Date: 2025-01-29 16:57:48.161335

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op
from letta.settings import settings

# revision identifiers, used by Alembic.
revision: str = "f922ca16e42c"
down_revision: Union[str, None] = "6fbe9cace832"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("agents", sa.Column("project_id", sa.String(), nullable=True))
    op.add_column("agents", sa.Column("template_id", sa.String(), nullable=True))
    op.add_column("agents", sa.Column("base_template_id", sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("agents", "base_template_id")
    op.drop_column("agents", "template_id")
    op.drop_column("agents", "project_id")
    # ### end Alembic commands ###
