"""Add callback data to jobs table

Revision ID: a3c7d62e08ca
Revises: 7b189006c97d
Create Date: 2025-04-17 17:40:16.224424

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op
from letta.settings import settings

# revision identifiers, used by Alembic.
revision: str = "a3c7d62e08ca"
down_revision: Union[str, None] = "7b189006c97d"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("jobs", sa.Column("callback_url", sa.String(), nullable=True))
    op.add_column("jobs", sa.Column("callback_sent_at", sa.DateTime(), nullable=True))
    op.add_column("jobs", sa.Column("callback_status_code", sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("jobs", "callback_status_code")
    op.drop_column("jobs", "callback_sent_at")
    op.drop_column("jobs", "callback_url")
    # ### end Alembic commands ###
