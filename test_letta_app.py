#!/usr/bin/env python3
"""
Letta AI应用测试脚本
使用ModelScope的Qwen模型构建智能助手
"""

import os
import time
from letta_client import CreateBlock, Letta, MessageCreate

# 设置环境变量
os.environ["OPENAI_API_KEY"] = "ms-3d98807d-54de-4c4b-93c2-38d79c93f004"
os.environ["OPENAI_API_BASE"] = "https://api-inference.modelscope.cn/v1"

def main():
    print("🚀 开始测试Letta AI应用...")
    
    # 连接到Letta服务器
    client = Letta(base_url="http://localhost:8283")
    print("✅ 成功连接到Letta服务器")
    
    try:
        # 创建一个智能助手
        print("\n📝 创建智能助手...")
        agent_state = client.agents.create(
            name="智能助手",
            memory_blocks=[
                CreateBlock(
                    label="human",
                    value="用户名：张三，职业：软件工程师，兴趣：AI技术和编程",
                ),
                CreateBlock(
                    label="persona", 
                    value="你是一个专业的AI助手，擅长技术问题解答和编程指导。你会用中文回答，语气友好专业。",
                ),
            ],
            # 使用默认模型（会自动使用配置的API）
            model="gpt-4o-mini",  # 这会使用我们配置的ModelScope API
            embedding="text-embedding-3-small",
        )
        
        print(f"✅ 成功创建智能助手: {agent_state.name} (ID: {agent_state.id})")
        
        # 与智能助手对话
        print("\n💬 开始对话测试...")
        
        # 第一轮对话
        response1 = client.agents.messages.create(
            agent_id=agent_state.id,
            messages=[
                MessageCreate(
                    role="user",
                    content="你好！我想学习Python编程，你能给我一些建议吗？"
                )
            ],
        )
        
        print("🤖 助手回复:")
        for msg in response1.messages:
            if hasattr(msg, 'content') and msg.content:
                print(f"   {msg.content}")
        
        time.sleep(2)
        
        # 第二轮对话
        response2 = client.agents.messages.create(
            agent_id=agent_state.id,
            messages=[
                MessageCreate(
                    role="user",
                    content="能推荐一些适合初学者的Python项目吗？"
                )
            ],
        )
        
        print("\n🤖 助手回复:")
        for msg in response2.messages:
            if hasattr(msg, 'content') and msg.content:
                print(f"   {msg.content}")
        
        # 显示使用统计
        print(f"\n📊 使用统计:")
        print(f"   Token使用量: {response2.usage}")
        
        # 列出所有智能体
        agents = client.agents.list()
        print(f"\n📋 当前智能体列表 (共{len(agents)}个):")
        for agent in agents:
            print(f"   - {agent.name} (ID: {agent.id})")
        
        print("\n🎉 测试完成！智能助手运行正常。")
        
        # 询问是否删除测试智能体
        delete_choice = input("\n❓ 是否删除测试智能体？(y/n): ").lower().strip()
        if delete_choice == 'y':
            client.agents.delete(agent_id=agent_state.id)
            print("🗑️ 已删除测试智能体")
        else:
            print("💾 保留测试智能体，您可以继续使用")
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        print("请检查:")
        print("1. Letta服务器是否正在运行 (http://localhost:8283)")
        print("2. ModelScope API密钥是否正确")
        print("3. 网络连接是否正常")

if __name__ == "__main__":
    main()
