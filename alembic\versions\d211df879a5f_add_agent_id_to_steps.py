"""add agent id to steps

Revision ID: d211df879a5f
Revises: 2f4ede6ae33b
Create Date: 2025-03-06 21:42:22.289345

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op
from letta.settings import settings

# revision identifiers, used by Alembic.
revision: str = "d211df879a5f"
down_revision: Union[str, None] = "2f4ede6ae33b"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("steps", sa.Column("agent_id", sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("steps", "agent_id")
    # ### end Alembic commands ###
