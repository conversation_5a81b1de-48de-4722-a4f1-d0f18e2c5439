[pytest]
pythonpath = /letta
testpaths = /tests
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function
filterwarnings =
    ignore::pytest.PytestRemovedIn9Warning
    # suppresses the warnings we see with the event_loop fixture
    ignore:.*is asynchronous and explicitly requests the "event_loop" fixture. Asynchronous fixtures and test functions should use "asyncio.get_running_loop\(\)" instead.*
markers =
    local_sandbox: mark test as part of local sandbox tests
    e2b_sandbox: mark test as part of E2B sandbox tests
    openai_basic: Tests for OpenAI endpoints
    anthropic_basic: Tests for Anthropic endpoints
    azure_basic: Tests for Azure endpoints
    gemini_basic: Tests for Gemini endpoints
