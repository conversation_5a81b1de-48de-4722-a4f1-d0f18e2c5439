"""add index for agent_tags reversed order

Revision ID: f55542f37641
Revises: ddecfe4902bc
Create Date: 2025-07-24 18:00:30.773048

"""

from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "f55542f37641"
down_revision: Union[str, None] = "f5d26b0526e8"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Note some issues at least with pg8000 with concurrent index creation
    # with op.get_context().autocommit_block():
    #     op.create_index(
    #         op.f('ix_agent_tags_tag_agent_id'),
    #         "agents_tags",
    #         ['tag', 'agent_id'],
    #         unique=False,
    #         postgresql_concurrently=True,
    #     )
    op.create_index("ix_agents_tags_tag_agent_id", "agents_tags", ["tag", "agent_id"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_agents_tags_tag_agent_id", table_name="agents_tags")
    # ### end Alembic commands ###
