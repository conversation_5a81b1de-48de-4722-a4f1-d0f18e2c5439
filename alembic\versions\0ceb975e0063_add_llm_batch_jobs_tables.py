"""Add LLM batch jobs tables

Revision ID: 0ceb975e0063
Revises: 90bb156e71df
Create Date: 2025-04-07 15:57:18.475151

"""

from typing import Sequence, Union

import sqlalchemy as sa

import letta
from alembic import op
from letta.settings import settings

# revision identifiers, used by Alembic.
revision: str = "0ceb975e0063"
down_revision: Union[str, None] = "90bb156e71df"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "llm_batch_job",
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("status", sa.String(), nullable=False),
        sa.Column("llm_provider", sa.String(), nullable=False),
        sa.Column("create_batch_response", letta.orm.custom_columns.CreateBatchResponseColumn(), nullable=False),
        sa.Column("latest_polling_response", letta.orm.custom_columns.PollBatchResponseColumn(), nullable=True),
        sa.Column("last_polled_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("is_deleted", sa.Boolean(), server_default=sa.text("FALSE"), nullable=False),
        sa.Column("_created_by_id", sa.String(), nullable=True),
        sa.Column("_last_updated_by_id", sa.String(), nullable=True),
        sa.Column("organization_id", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["organization_id"],
            ["organizations.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index("ix_llm_batch_job_created_at", "llm_batch_job", ["created_at"], unique=False)
    op.create_index("ix_llm_batch_job_status", "llm_batch_job", ["status"], unique=False)
    op.create_table(
        "llm_batch_items",
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("batch_id", sa.String(), nullable=False),
        sa.Column("llm_config", letta.orm.custom_columns.LLMConfigColumn(), nullable=False),
        sa.Column("request_status", sa.String(), nullable=False),
        sa.Column("step_status", sa.String(), nullable=False),
        sa.Column("step_state", letta.orm.custom_columns.AgentStepStateColumn(), nullable=False),
        sa.Column("batch_request_result", letta.orm.custom_columns.BatchRequestResultColumn(), nullable=True),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("is_deleted", sa.Boolean(), server_default=sa.text("FALSE"), nullable=False),
        sa.Column("_created_by_id", sa.String(), nullable=True),
        sa.Column("_last_updated_by_id", sa.String(), nullable=True),
        sa.Column("organization_id", sa.String(), nullable=False),
        sa.Column("agent_id", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(["agent_id"], ["agents.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["batch_id"], ["llm_batch_job.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(
            ["organization_id"],
            ["organizations.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index("ix_llm_batch_items_agent_id", "llm_batch_items", ["agent_id"], unique=False)
    op.create_index("ix_llm_batch_items_batch_id", "llm_batch_items", ["batch_id"], unique=False)
    op.create_index("ix_llm_batch_items_status", "llm_batch_items", ["request_status"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_llm_batch_items_status", table_name="llm_batch_items")
    op.drop_index("ix_llm_batch_items_batch_id", table_name="llm_batch_items")
    op.drop_index("ix_llm_batch_items_agent_id", table_name="llm_batch_items")
    op.drop_table("llm_batch_items")
    op.drop_index("ix_llm_batch_job_status", table_name="llm_batch_job")
    op.drop_index("ix_llm_batch_job_created_at", table_name="llm_batch_job")
    op.drop_table("llm_batch_job")
    # ### end Alembic commands ###
