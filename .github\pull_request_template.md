**Please describe the purpose of this pull request.**
Is it to add a new feature? Is it to fix a bug?

**How to test**
How can we test your PR during review? What commands should we run? What outcomes should we expect?

**Have you tested this PR?**
Have you tested the latest commit on the PR? If so please provide outputs from your tests.

**Related issues or PRs**
Please link any related GitHub [issues](https://github.com/letta-ai/letta/issues) or [PRs](https://github.com/letta-ai/letta/pulls).

**Is your PR over 500 lines of code?**
If so, please break up your PR into multiple smaller PRs so that we can review them quickly, or provide justification for its length.

**Additional context**
Add any other context or screenshots about the PR here.
