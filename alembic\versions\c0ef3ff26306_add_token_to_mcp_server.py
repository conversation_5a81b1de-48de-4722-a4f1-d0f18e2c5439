"""add_token_to_mcp_server

Revision ID: c0ef3ff26306
Revises: 1c6b6a38b713
Create Date: 2025-06-14 14:59:53.835883

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op
from letta.settings import settings

# revision identifiers, used by Alembic.
revision: str = "c0ef3ff26306"
down_revision: Union[str, None] = "1c6b6a38b713"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("mcp_server", sa.Column("token", sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("mcp_server", "token")
    # ### end Alembic commands ###
