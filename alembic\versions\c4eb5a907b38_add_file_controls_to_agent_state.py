"""Add file controls to agent state

Revision ID: c4eb5a907b38
Revises: cce9a6174366
Create Date: 2025-07-21 15:56:57.413000

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "c4eb5a907b38"
down_revision: Union[str, None] = "cce9a6174366"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("agents", sa.Column("max_files_open", sa.Integer(), nullable=True))
    op.add_column("agents", sa.Column("per_file_view_window_char_limit", sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("agents", "per_file_view_window_char_limit")
    op.drop_column("agents", "max_files_open")
    # ### end Alembic commands ###
