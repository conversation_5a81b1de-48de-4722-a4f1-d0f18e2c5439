"""Remove module field on tool

Revision ID: 22a6e413d89c
Revises: 88f9432739a9
Create Date: 2025-01-10 17:38:23.811795

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op
from letta.settings import settings

# revision identifiers, used by Alembic.
revision: str = "22a6e413d89c"
down_revision: Union[str, None] = "88f9432739a9"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("tools", "module")
    # ### end Alembic commands ###


def downgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("tools", sa.Column("module", sa.VARCHAR(), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
