"""add byok fields and unique constraint

Revision ID: 373dabcba6cf
Revises: c56081a05371
Create Date: 2025-04-30 19:38:25.010856

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op
from letta.settings import settings

# revision identifiers, used by Alembic.
revision: str = "373dabcba6cf"
down_revision: Union[str, None] = "c56081a05371"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("providers", sa.Column("provider_type", sa.String(), nullable=True))
    op.add_column("providers", sa.Column("base_url", sa.String(), nullable=True))
    op.create_unique_constraint("unique_name_organization_id", "providers", ["name", "organization_id"])
    # ### end Alembic commands ###


def downgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("unique_name_organization_id", "providers", type_="unique")
    op.drop_column("providers", "base_url")
    op.drop_column("providers", "provider_type")
    # ### end Alembic commands ###
