"""add_run_completion_and_duration_to_agents_table

Revision ID: 9758adf8fdd3
Revises: 9556081ce65b
Create Date: 2025-06-18 18:22:31.135685

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op
from letta.settings import settings

# revision identifiers, used by Alembic.
revision: str = "9758adf8fdd3"
down_revision: Union[str, None] = "9556081ce65b"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("agents", sa.Column("last_run_completion", sa.DateTime(timezone=True), nullable=True))
    op.add_column("agents", sa.Column("last_run_duration_ms", sa.<PERSON><PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("agents", "last_run_duration_ms")
    op.drop_column("agents", "last_run_completion")
    # ### end Alembic commands ###
