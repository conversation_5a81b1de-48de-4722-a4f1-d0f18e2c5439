"""adding request_config to Job table

Revision ID: f595e0e8013e
Revises: 7f652fdd3dba
Create Date: 2025-01-14 14:34:34.203363

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op
from letta.settings import settings

# revision identifiers, used by Alembic.
revision: str = "f595e0e8013e"
down_revision: Union[str, None] = "7f652fdd3dba"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("jobs", sa.Column("request_config", sa.JSON, nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("jobs", "request_config")
    # ### end Alembic commands ###
