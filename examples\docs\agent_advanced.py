from letta_client import <PERSON><PERSON><PERSON><PERSON>, Letta, MessageCreate

from letta.prompts import gpt_system

"""
Make sure you run the Letta server before running this example.
```
letta server
```
"""

client = Letta(base_url="http://localhost:8283")

# create a new agent
agent_state = client.agents.create(
    # agent's name (unique per-user, autogenerated if not provided)
    name="agent_name",
    # in-context memory representation with human/persona blocks
    memory_blocks=[
        CreateBlock(
            label="human",
            value="Name: <PERSON>",
        ),
        CreateBlock(
            label="persona",
            value="You are a helpful assistant that loves emojis",
        ),
    ],
    # LLM model & endpoint configuration
    model="openai/gpt-4o-mini",
    context_window_limit=8000,
    # embedding model & endpoint configuration (cannot be changed)
    embedding="openai/text-embedding-3-small",
    # system instructions for the agent (defaults to `memgpt_chat`)
    system=gpt_system.get_system_text("memgpt_chat"),
    # whether to include base letta tools (default: True)
    include_base_tools=True,
    # list of additional tools (by name) to add to the agent
    tool_ids=[],
)
print(f"Created agent with name {agent_state.name} and unique ID {agent_state.id}")

# message an agent as a user
response = client.agents.messages.create(
    agent_id=agent_state.id, 
    messages=[
        MessageCreate(
            role="user",
            content="hello",
        )
    ],
)
print("Usage", response.usage)
print("Agent messages", response.messages)

# message a system message (non-user)
response = client.agents.messages.create(
    agent_id=agent_state.id, 
    messages=[
        MessageCreate(
            role="system",
            content="[system] user has logged in. send a friendly message.",
        )
    ],
)
print("Usage", response.usage)
print("Agent messages", response.messages)

# delete the agent
client.agents.delete(agent_id=agent_state.id)
