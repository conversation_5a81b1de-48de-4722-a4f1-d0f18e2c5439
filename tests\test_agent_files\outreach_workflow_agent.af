{"agent_type": "memgpt_agent", "core_memory": [], "created_at": "2025-04-01T03:46:31", "description": "An simple workflow agent that has memory disabled, so that each request is independent.", "embedding_config": {"embedding_endpoint_type": "openai", "embedding_endpoint": "https://api.openai.com/v1", "embedding_model": "text-embedding-ada-002", "embedding_dim": 1536, "embedding_chunk_size": 300, "handle": "openai/text-embedding-ada-002", "azure_endpoint": null, "azure_version": null, "azure_deployment": null}, "llm_config": {"model": "gpt-4o-mini", "model_endpoint_type": "openai", "model_endpoint": "https://api.openai.com/v1", "model_wrapper": null, "context_window": 32000, "put_inner_thoughts_in_kwargs": true, "handle": "openai/gpt-4o-mini", "temperature": 0.7, "max_tokens": 4096, "enable_reasoner": false, "max_reasoning_tokens": 0}, "message_buffer_autoclear": true, "in_context_message_indices": [0], "messages": [{"created_at": "2025-04-01T03:46:31.172913", "group_id": null, "model": "gpt-4o-mini", "name": null, "role": "system", "content": [{"type": "text", "text": "\n### Memory [last modified: 2025-03-31 08:46:31 PM PDT-0700]\n0 previous messages between you and the user are stored in recall memory (use functions to access them)\n0 total memories you created are stored in archival memory (use functions to access them)\n\n\nCore memory shown below (limited in size, additional information stored in archival / recall memory):\n"}], "tool_call_id": null, "tool_calls": [], "tool_returns": [], "updated_at": "2025-04-01T03:46:31"}, {"created_at": "2025-04-01T03:46:44.910359", "group_id": null, "model": null, "name": null, "role": "user", "content": [{"type": "text", "text": "{\n  \"type\": \"user_message\",\n  \"message\": \"charles packer\",\n  \"time\": \"2025-03-31 08:46:44 PM PDT-0700\"\n}"}], "tool_call_id": null, "tool_calls": [], "tool_returns": [], "updated_at": "2025-04-01T03:46:46"}, {"created_at": "2025-04-01T03:46:45.954774", "group_id": null, "model": "gpt-4o-mini", "name": "outreach_workflow_agent", "role": "assistant", "content": [{"type": "text", "text": "User is asking about a specific candidate named <PERSON>. I need to retrieve information on this candidate for evaluation."}], "tool_call_id": null, "tool_calls": [{"id": "87d998a6-511e-41c6-bf4d-8a178", "function": {"arguments": "{\n  \"candidate_name\": \"<PERSON>\",\n  \"request_heartbeat\": true\n}", "name": "retrieve_candidate"}, "type": "function"}], "tool_returns": [], "updated_at": "2025-04-01T03:46:46"}, {"created_at": "2025-04-01T03:46:45.991088", "group_id": null, "model": "gpt-4o-mini", "name": "outreach_workflow_agent", "role": "tool", "content": [{"type": "text", "text": "{\n  \"status\": \"OK\",\n  \"message\": \"The candidate <PERSON> is a software engineer working at Letta and based in SF\",\n  \"time\": \"2025-03-31 08:46:45 PM PDT-0700\"\n}"}], "tool_call_id": "87d998a6-511e-41c6-bf4d-8a178", "tool_calls": [], "tool_returns": [{"status": "success", "stdout": [], "stderr": []}], "updated_at": "2025-04-01T03:46:46"}, {"created_at": "2025-04-01T03:46:46.047490", "group_id": null, "model": "gpt-4o-mini", "name": null, "role": "user", "content": [{"type": "text", "text": "{\n  \"type\": \"heartbeat\",\n  \"reason\": \"[This is an automated system message hidden from the user] Function called using request_heartbeat=true, returning control\",\n  \"time\": \"2025-03-31 08:46:46 PM PDT-0700\"\n}"}], "tool_call_id": null, "tool_calls": [], "tool_returns": [], "updated_at": "2025-04-01T03:46:47"}, {"created_at": "2025-04-01T03:46:47.654624", "group_id": null, "model": "gpt-4o-mini", "name": "outreach_workflow_agent", "role": "assistant", "content": [{"type": "text", "text": "<PERSON> is a software engineer at Letta, based in San Francisco. I need to evaluate his qualifications and suitability for the role we have in mind. I will consider his experience, skills, and potential fit within the team."}], "tool_call_id": null, "tool_calls": [{"id": "ed84217d-a477-4995-bd75-8334b", "function": {"arguments": "{\n  \"request_heartbeat\": true\n}", "name": "evaluate_candidate"}, "type": "function"}], "tool_returns": [], "updated_at": "2025-04-01T03:46:47"}, {"created_at": "2025-04-01T03:46:47.664287", "group_id": null, "model": "gpt-4o-mini", "name": "outreach_workflow_agent", "role": "tool", "content": [{"type": "text", "text": "{\n  \"status\": \"OK\",\n  \"message\": \"False\",\n  \"time\": \"2025-03-31 08:46:47 PM PDT-0700\"\n}"}], "tool_call_id": "ed84217d-a477-4995-bd75-8334b", "tool_calls": [], "tool_returns": [{"status": "success", "stdout": [], "stderr": []}], "updated_at": "2025-04-01T03:46:47"}, {"created_at": "2025-04-01T03:46:47.708878", "group_id": null, "model": "gpt-4o-mini", "name": null, "role": "user", "content": [{"type": "text", "text": "{\n  \"type\": \"heartbeat\",\n  \"reason\": \"[This is an automated system message hidden from the user] Function called using request_heartbeat=true, returning control\",\n  \"time\": \"2025-03-31 08:46:47 PM PDT-0700\"\n}"}], "tool_call_id": null, "tool_calls": [], "tool_returns": [], "updated_at": "2025-04-01T03:46:49"}, {"created_at": "2025-04-01T03:46:48.942481", "group_id": null, "model": "gpt-4o-mini", "name": "outreach_workflow_agent", "role": "assistant", "content": [{"type": "text", "text": "After evaluating <PERSON>, it has been determined that he is not a suitable candidate for the position we have available. I will proceed to formally reject him."}], "tool_call_id": null, "tool_calls": [{"id": "a45b7931-bd04-4781-874b-64446", "function": {"arguments": "{\n  \"candidate_name\": \"<PERSON>\",\n  \"request_heartbeat\": true\n}", "name": "reject"}, "type": "function"}], "tool_returns": [], "updated_at": "2025-04-01T03:46:49"}, {"created_at": "2025-04-01T03:46:48.970292", "group_id": null, "model": "gpt-4o-mini", "name": "outreach_workflow_agent", "role": "tool", "content": [{"type": "text", "text": "{\n  \"status\": \"OK\",\n  \"message\": \"None\",\n  \"time\": \"2025-03-31 08:46:48 PM PDT-0700\"\n}"}], "tool_call_id": "a45b7931-bd04-4781-874b-64446", "tool_calls": [], "tool_returns": [{"status": "success", "stdout": ["Rejecting <PERSON>\n"], "stderr": []}], "updated_at": "2025-04-01T03:46:49"}], "metadata_": null, "multi_agent_group": null, "name": "outreach_workflow_agent", "system": "", "tags": [], "tool_exec_environment_variables": [], "tool_rules": [{"tool_name": "retrieve_candidate", "type": "run_first"}, {"tool_name": "retrieve_candidate", "type": "constrain_child_tools", "children": ["evaluate_candidate"]}, {"tool_name": "evaluate_candidate", "type": "conditional", "default_child": "reject", "child_output_mapping": {"True": "send_email", "False": "reject"}, "require_output_mapping": false}, {"tool_name": "reject", "type": "exit_loop"}, {"tool_name": "send_email", "type": "exit_loop"}], "tools": [{"args_json_schema": null, "created_at": "2025-04-01T03:46:30", "description": "Retrieve a candidate to evaluate.", "json_schema": {"name": "retrieve_candidate", "description": "Retrieve a candidate to evaluate.", "parameters": {"type": "object", "properties": {"candidate_name": {"type": "string", "description": "The name of the candidate to retrieve. "}, "request_heartbeat": {"type": "boolean", "description": "Request an immediate heartbeat after function execution. Set to `True` if you want to send a follow-up message or run a follow-up function."}}, "required": ["candidate_name", "request_heartbeat"]}, "type": null, "required": []}, "name": "retrieve_candidate", "return_char_limit": 6000, "source_code": "def retrieve_candidate(candidate_name: str): \n    \"\"\" \n    Retrieve a candidate to evaluate.\n\n    Args: \n        candidate_name (str): The name of the candidate to retrieve. \n    \"\"\"\n    import random\n    return f\"The candidate {candidate_name} is a software engineer working at Letta and based in SF\"\n", "source_type": "python", "tags": [], "tool_type": "custom", "updated_at": "2025-04-01T03:46:30", "metadata_": {}}, {"args_json_schema": null, "created_at": "2025-04-01T03:46:30", "description": "Send an email to the candidate. ", "json_schema": {"name": "send_email", "description": "Send an email to the candidate. ", "parameters": {"type": "object", "properties": {"candidate_name": {"type": "string", "description": "The name of the candidate to send the email to. "}, "email_subject": {"type": "string", "description": "The subject of the email. "}, "email_content": {"type": "string", "description": "The content of the email. "}, "request_heartbeat": {"type": "boolean", "description": "Request an immediate heartbeat after function execution. Set to `True` if you want to send a follow-up message or run a follow-up function."}}, "required": ["candidate_name", "email_subject", "email_content", "request_heartbeat"]}, "type": null, "required": []}, "name": "send_email", "return_char_limit": 6000, "source_code": "def send_email(candidate_name: str, email_subject: str, email_content: str): \n    \"\"\" \n    Send an email to the candidate. \n\n    Args: \n        candidate_name (str): The name of the candidate to send the email to. \n        email_subject (str): The subject of the email. \n        email_content (str): The content of the email. \n    \"\"\"\n    print(f\"Sending email to {candidate_name}\")\n    return f\"\"\"\n    Subject: {email_subject}\n\n    {email_content}\n\n    \"\"\"\n", "source_type": "python", "tags": [], "tool_type": "custom", "updated_at": "2025-04-01T03:46:30", "metadata_": {}}, {"args_json_schema": null, "created_at": "2025-04-01T03:46:30", "description": "Evaluate the candidate. Return True to move forward, False to reject them.", "json_schema": {"name": "evaluate_candidate", "description": "Evaluate the candidate. Return True to move forward, False to reject them.", "parameters": {"type": "object", "properties": {"request_heartbeat": {"type": "boolean", "description": "Request an immediate heartbeat after function execution. Set to `True` if you want to send a follow-up message or run a follow-up function."}}, "required": ["request_heartbeat"]}, "type": null, "required": []}, "name": "evaluate_candidate", "return_char_limit": 6000, "source_code": "def evaluate_candidate(): \n    \"\"\" Evaluate the candidate. Return True to move forward, False to reject them.\"\"\"\n    import random\n    return random.random() > 0.5\n", "source_type": "python", "tags": [], "tool_type": "custom", "updated_at": "2025-04-01T03:46:30", "metadata_": {}}, {"args_json_schema": null, "created_at": "2025-04-01T03:46:30", "description": "Reject the candidate. ", "json_schema": {"name": "reject", "description": "Reject the candidate. ", "parameters": {"type": "object", "properties": {"candidate_name": {"type": "string", "description": "The name of the candidate to reject. "}, "request_heartbeat": {"type": "boolean", "description": "Request an immediate heartbeat after function execution. Set to `True` if you want to send a follow-up message or run a follow-up function."}}, "required": ["candidate_name", "request_heartbeat"]}, "type": null, "required": []}, "name": "reject", "return_char_limit": 6000, "source_code": "def reject(candidate_name: str): \n    \"\"\"\n    Reject the candidate. \n\n    Args: \n        candidate_name (str): The name of the candidate to reject. \n    \"\"\"\n    print(f\"Rejecting {candidate_name}\")\n", "source_type": "python", "tags": [], "tool_type": "custom", "updated_at": "2025-04-01T03:46:30", "metadata_": {}}], "updated_at": "2025-04-01T03:46:49.043727", "version": "0.6.47"}