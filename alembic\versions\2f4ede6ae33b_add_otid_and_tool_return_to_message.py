"""add otid and tool return to message

Revision ID: 2f4ede6ae33b
Revises: 54f2311edb62
Create Date: 2025-03-05 10:04:34.717671

"""

from typing import Sequence, Union

import sqlalchemy as sa

import letta.orm
from alembic import op
from letta.settings import settings

# revision identifiers, used by Alembic.
revision: str = "2f4ede6ae33b"
down_revision: Union[str, None] = "54f2311edb62"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("messages", sa.Column("otid", sa.String(), nullable=True))
    op.add_column("messages", sa.Column("tool_returns", letta.orm.custom_columns.ToolReturnColumn(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("messages", "tool_returns")
    op.drop_column("messages", "otid")
    # ### end Alembic commands ###
