#!/usr/bin/env python3
"""
Letta AI应用最终演示
使用Docker部署的Letta + ModelScope API
"""

import requests
import json
import time

def create_ai_assistant():
    """创建AI助手并进行对话演示"""
    base_url = "http://localhost:8283"
    
    print("🚀 Letta AI应用演示")
    print("=" * 50)
    
    # 等待服务启动
    print("⏳ 检查服务状态...")
    try:
        response = requests.get(f"{base_url}/v1/agents/", timeout=10)
        if response.status_code == 200:
            print("✅ Letta服务器运行正常")
        else:
            print("❌ 服务器响应异常")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        return False
    
    # 创建智能助手
    print("\n📝 创建AI智能助手...")
    
    payload = {
        "name": "AI_Programming_Assistant",
        "memory_blocks": [
            {
                "label": "human",
                "value": "用户：张三，职业：Python开发工程师，正在学习AI应用开发"
            },
            {
                "label": "persona", 
                "value": "你是一个专业的AI编程助手，擅长Python、AI和软件开发。你会用中文回答，语气友好专业，并提供实用的代码示例。"
            }
        ],
        # 使用OpenAI兼容的模型格式，会通过我们配置的ModelScope API路由
        "model": "openai/gpt-4o-mini",
        "embedding": "openai/text-embedding-ada-002"
    }
    
    try:
        create_response = requests.post(
            f"{base_url}/v1/agents/", 
            json=payload, 
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if create_response.status_code == 200:
            agent_data = create_response.json()
            agent_id = agent_data.get('id')
            print(f"✅ 成功创建智能助手: {agent_data.get('name')}")
            print(f"   助手ID: {agent_id}")
            
            # 进行对话演示
            print(f"\n💬 开始AI对话演示...")
            
            conversations = [
                "你好！我想学习如何使用Letta构建AI应用，能给我一些指导吗？",
                "能否给我一个简单的Python代码示例，展示如何使用Letta客户端？",
                "我想了解Letta的核心优势是什么？"
            ]
            
            for i, question in enumerate(conversations, 1):
                print(f"\n🙋‍♂️ 用户问题 {i}: {question}")
                
                message_payload = {
                    "messages": [
                        {
                            "role": "user",
                            "content": question
                        }
                    ]
                }
                
                try:
                    message_response = requests.post(
                        f"{base_url}/v1/agents/{agent_id}/messages",
                        json=message_payload,
                        headers={"Content-Type": "application/json"},
                        timeout=60
                    )
                    
                    if message_response.status_code == 200:
                        message_data = message_response.json()
                        messages = message_data.get('messages', [])
                        
                        print(f"🤖 AI助手回复:")
                        for msg in messages:
                            if isinstance(msg, dict) and msg.get('role') == 'assistant':
                                content = msg.get('content', '')
                                if content:
                                    # 限制显示长度
                                    if len(content) > 300:
                                        content = content[:300] + "..."
                                    print(f"   {content}")
                        
                        # 显示使用统计
                        usage = message_data.get('usage', {})
                        if usage:
                            print(f"   📊 Token使用: {usage}")
                    else:
                        print(f"❌ 对话失败: {message_response.text}")
                        
                except Exception as e:
                    print(f"❌ 对话请求异常: {e}")
                
                # 短暂延迟
                time.sleep(2)
            
            return agent_id
            
        else:
            print(f"❌ 创建智能助手失败: {create_response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 创建请求异常: {e}")
        return None

def show_usage_guide():
    """显示使用指南"""
    print(f"\n🎯 Letta AI应用使用指南")
    print("=" * 50)
    print("📋 服务信息:")
    print("   • 服务器地址: http://localhost:8283")
    print("   • API文档: http://localhost:8283/docs")
    print("   • Web界面: https://app.letta.com (连接本地服务器)")
    
    print(f"\n🛠️ 开发指南:")
    print("   1. 使用Python客户端:")
    print("      pip install letta_client")
    print("      from letta_client import Letta")
    print("      client = Letta(base_url='http://localhost:8283')")
    
    print(f"\n   2. 创建智能体:")
    print("      agent = client.agents.create(")
    print("          name='我的助手',")
    print("          model='openai-proxy/Qwen/Qwen3-Coder-480B-A35B-Instruct'")
    print("      )")
    
    print(f"\n   3. 与智能体对话:")
    print("      response = client.agents.messages.create(")
    print("          agent_id=agent.id,")
    print("          messages=[{'role': 'user', 'content': '你好'}]")
    print("      )")
    
    print(f"\n🔧 Docker管理命令:")
    print("   • 查看容器状态: docker ps")
    print("   • 查看日志: docker logs letta_server")
    print("   • 重启服务: docker restart letta_server")
    print("   • 停止服务: docker stop letta_server letta_postgres")

def main():
    """主函数"""
    print("🌟 欢迎使用Letta AI应用平台！")
    print("基于Docker + ModelScope API + Qwen模型")
    
    # 创建并测试AI助手
    agent_id = create_ai_assistant()
    
    if agent_id:
        print(f"\n🎉 演示完成！AI助手创建成功")
        print(f"助手ID: {agent_id}")
        
        # 显示使用指南
        show_usage_guide()
        
        print(f"\n✨ 您现在可以:")
        print("   1. 访问 https://app.letta.com 使用Web界面")
        print("   2. 使用Python客户端进行开发")
        print("   3. 通过REST API集成到您的应用中")
        
    else:
        print(f"\n❌ 演示失败，请检查:")
        print("   1. Docker容器是否正常运行")
        print("   2. ModelScope API密钥是否有效")
        print("   3. 网络连接是否正常")

if __name__ == "__main__":
    main()
