name: Sync Code

on:
  push:
    branches:
      - main

jobs:
  notify:
    runs-on: ubuntu-latest
    if: ${{ !contains(github.event.head_commit.message, '[sync-skip]') }}
    steps:
      - name: Trigger repository_dispatch
        run: |
          curl -X POST \
            -H "Authorization: token ${{ secrets.SYNC_PAT }}" \
            -H "Accept: application/vnd.github.v3+json" \
            https://api.github.com/repos/letta-ai/letta-cloud/dispatches \
            -d '{"event_type":"oss-update"}'
