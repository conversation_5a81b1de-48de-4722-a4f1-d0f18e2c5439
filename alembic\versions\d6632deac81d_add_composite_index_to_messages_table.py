"""Add composite index to messages table

Revision ID: d6632deac81d
Revises: 54dec07619c4
Create Date: 2024-12-18 13:38:56.511701

"""

from typing import Sequence, Union

from alembic import op
from letta.settings import settings

# revision identifiers, used by Alembic.
revision: str = "d6632deac81d"
down_revision: Union[str, None] = "54dec07619c4"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index("ix_messages_agent_created_at", "messages", ["agent_id", "created_at"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_messages_agent_created_at", table_name="messages")
    # ### end Alembic commands ###
