#!/usr/bin/env python3
"""
测试Docker版本的Letta服务
"""

import requests
import json
import time

def test_docker_letta():
    """测试Docker版本的Letta服务"""
    base_url = "http://localhost:8283"
    
    print("🐳 测试Docker版本的Letta服务...")
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    for i in range(30):  # 等待最多30秒
        try:
            response = requests.get(f"{base_url}/", timeout=5)
            if response.status_code == 200:
                print("✅ 服务器已启动")
                break
        except:
            pass
        time.sleep(1)
        print(f"   等待中... ({i+1}/30)")
    else:
        print("❌ 服务器启动超时")
        return False
    
    # 测试健康状态
    try:
        print("\n🔍 测试API端点...")
        
        # 测试agents端点
        response = requests.get(f"{base_url}/v1/agents/", timeout=10)
        print(f"agents端点状态码: {response.status_code}")
        
        if response.status_code == 200:
            agents = response.json()
            print(f"✅ 当前智能体数量: {len(agents)}")
            
            # 尝试创建一个简单的智能体
            print("\n📝 尝试创建智能体...")
            
            payload = {
                "name": "docker_test_agent",
                "memory_blocks": [
                    {
                        "label": "human",
                        "value": "User: Test User"
                    },
                    {
                        "label": "persona",
                        "value": "You are a helpful AI assistant."
                    }
                ],
                "model": "openai/gpt-4o-mini",  # 使用标准模型名，会通过我们的API配置路由到ModelScope
                "embedding": "openai/text-embedding-ada-002"  # 使用兼容的embedding模型
            }
            
            create_response = requests.post(
                f"{base_url}/v1/agents/", 
                json=payload, 
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            print(f"创建智能体状态码: {create_response.status_code}")
            
            if create_response.status_code == 200:
                agent_data = create_response.json()
                print(f"✅ 成功创建智能体: {agent_data.get('name')} (ID: {agent_data.get('id')})")
                
                # 尝试发送消息
                agent_id = agent_data.get('id')
                print(f"\n💬 尝试与智能体对话...")
                
                message_payload = {
                    "messages": [
                        {
                            "role": "user",
                            "content": "Hello! Can you introduce yourself?"
                        }
                    ]
                }
                
                message_response = requests.post(
                    f"{base_url}/v1/agents/{agent_id}/messages",
                    json=message_payload,
                    headers={"Content-Type": "application/json"},
                    timeout=60
                )
                
                print(f"发送消息状态码: {message_response.status_code}")
                
                if message_response.status_code == 200:
                    message_data = message_response.json()
                    print("✅ 对话成功！")
                    print(f"响应消息数量: {len(message_data.get('messages', []))}")
                    
                    # 显示部分响应
                    for msg in message_data.get('messages', [])[:2]:
                        if hasattr(msg, 'get') and msg.get('content'):
                            print(f"   🤖: {msg['content'][:100]}...")
                else:
                    print(f"❌ 对话失败: {message_response.text}")
                
                return True
            else:
                print(f"❌ 创建智能体失败: {create_response.text}")
                return False
        else:
            print(f"❌ agents端点访问失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False

def main():
    print("🚀 开始测试Docker版本的Letta...")
    
    success = test_docker_letta()
    
    if success:
        print("\n🎉 Docker版本的Letta测试成功！")
        print("📋 服务信息:")
        print("   - 服务器地址: http://localhost:8283")
        print("   - Web界面: https://app.letta.com (连接本地服务器)")
        print("   - API文档: http://localhost:8283/docs")
    else:
        print("\n❌ Docker版本的Letta测试失败")
        print("🔧 故障排除建议:")
        print("   1. 检查容器状态: docker ps")
        print("   2. 查看日志: docker logs letta_server")
        print("   3. 重启容器: docker restart letta_server")

if __name__ == "__main__":
    main()
