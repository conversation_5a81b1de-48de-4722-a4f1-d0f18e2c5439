"""add table to store mcp servers

Revision ID: 9ecbdbaa409f
Revises: 6c53224a7a58
Create Date: 2025-05-21 15:25:12.483026

"""

from typing import Sequence, Union

import sqlalchemy as sa

import letta
from alembic import op
from letta.settings import settings

# revision identifiers, used by Alembic.
revision: str = "9ecbdbaa409f"
down_revision: Union[str, None] = "6c53224a7a58"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mcp_server",
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("server_name", sa.String(), nullable=False),
        sa.Column("server_type", sa.String(), nullable=False),
        sa.Column("server_url", sa.String(), nullable=True),
        sa.Column("stdio_config", letta.orm.custom_columns.MCPStdioServerConfigColumn(), nullable=True),
        sa.Column("organization_id", sa.String(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), server_default=sa.text("FALSE"), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("_created_by_id", sa.String(), nullable=True),
        sa.Column("_last_updated_by_id", sa.String(), nullable=True),
        sa.Column("metadata_", sa.JSON(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.ForeignKeyConstraint(
            ["organization_id"],
            ["organizations.id"],
        ),
        sa.UniqueConstraint("server_name", "organization_id", name="uix_name_organization_mcp_server"),
    )


def downgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("mcp_server")
    # ### end Alembic commands ###
