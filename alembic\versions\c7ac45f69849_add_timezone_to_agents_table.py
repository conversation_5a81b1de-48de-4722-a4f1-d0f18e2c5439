"""Add timezone to agents table

Revision ID: c7ac45f69849
Revises: 61ee53ec45a5
Create Date: 2025-06-23 17:48:51.177458

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op
from letta.settings import settings

# revision identifiers, used by Alembic.
revision: str = "c7ac45f69849"
down_revision: Union[str, None] = "61ee53ec45a5"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("agents", sa.Column("timezone", sa.String(), nullable=True, default="UTC"))
    # ### end Alembic commands ###


def downgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("agents", "timezone")
    # ### end Alembic commands ###
