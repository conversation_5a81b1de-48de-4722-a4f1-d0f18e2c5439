"""add identities for blocks

Revision ID: 167491cfb7a8
Revises: d211df879a5f
Create Date: 2025-03-07 17:51:24.843275

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op
from letta.settings import settings

# revision identifiers, used by Alembic.
revision: str = "167491cfb7a8"
down_revision: Union[str, None] = "d211df879a5f"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "identities_blocks",
        sa.Column("identity_id", sa.String(), nullable=False),
        sa.Column("block_id", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(["block_id"], ["block.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["identity_id"], ["identities.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("identity_id", "block_id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("identities_blocks")
    # ### end Alembic commands ###
