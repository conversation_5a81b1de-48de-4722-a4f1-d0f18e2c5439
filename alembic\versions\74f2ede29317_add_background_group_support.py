"""add background group support

Revision ID: 74f2ede29317
Revises: bff040379479
Create Date: 2025-04-01 07:45:31.735977

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op
from letta.settings import settings

# revision identifiers, used by Alembic.
revision: str = "74f2ede29317"
down_revision: Union[str, None] = "bff040379479"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("groups", sa.Column("background_agents_interval", sa.Integer(), nullable=True))
    op.add_column("groups", sa.Column("turns_counter", sa.Integer(), nullable=True))
    op.add_column("groups", sa.Column("last_processed_message_id", sa.String(), nullable=True))
    op.create_table(
        "groups_blocks",
        sa.Column("group_id", sa.String(), nullable=False),
        sa.Column("block_id", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(["block_id"], ["block.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["group_id"], ["groups.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("group_id", "block_id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # Skip this migration for SQLite
    if not settings.letta_pg_uri_no_default:
        return

    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("groups_blocks")
    op.drop_column("groups", "last_processed_message_id")
    op.drop_column("groups", "turns_counter")
    op.drop_column("groups", "background_agents_interval")
    # ### end Alembic commands ###
